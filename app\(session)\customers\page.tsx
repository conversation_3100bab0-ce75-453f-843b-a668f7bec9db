import { ModernDashboardLayout } from "@/components/dashboard/modern-layout";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Users, UserPlus, Search, Filter } from "lucide-react";

export default function CustomersPage() {
  return (
    <ModernDashboardLayout 
      title="Customers" 
      subtitle="Manage your customer base and relationships"
    >
      <div className="space-y-6">
        {/* Coming Soon Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-3 text-neutral-900 dark:text-neutral-200">
              <Users className="w-5 h-5 text-[#ffbe98]" />
              Customer Management
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12">
              <Users className="w-16 h-16 text-neutral-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-neutral-900 dark:text-neutral-200 mb-2">
                Customer Management Coming Soon
              </h3>
              <p className="text-neutral-600 dark:text-neutral-400 max-w-md mx-auto">
                Comprehensive customer management tools are being developed. 
                You&apos;ll be able to view, search, and manage all your customers in one place.
              </p>
              <div className="mt-6 flex items-center justify-center gap-4 text-sm text-neutral-500">
                <div className="flex items-center gap-2">
                  <Search className="w-4 h-4" />
                  <span>Search & Filter</span>
                </div>
                <div className="flex items-center gap-2">
                  <UserPlus className="w-4 h-4" />
                  <span>Add Customers</span>
                </div>
                <div className="flex items-center gap-2">
                  <Filter className="w-4 h-4" />
                  <span>Advanced Filtering</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </ModernDashboardLayout>
  );
}
