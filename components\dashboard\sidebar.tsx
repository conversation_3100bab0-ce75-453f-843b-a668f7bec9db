"use client";

import { useState } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { motion, AnimatePresence } from "framer-motion";
import {
  LayoutDashboard,
  User,
  Settings,
  LogOut,
  ChevronLeft,
  ChevronRight,
  CreditCard,
  Bell,
  HelpCircle,
  Zap,
  Menu,
  X
} from "lucide-react";
import { cn } from "@/lib/utils";
import { authClient } from "@/lib/auth-client";
import { useRouter } from "next/navigation";

interface NavItem {
  path: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: string;
  description?: string;
}

const navItems: NavItem[] = [
  {
    path: "/dashboard",
    label: "Dashboard",
    icon: LayoutDashboard,
    description: "Overview and analytics"
  },
  {
    path: "/billing",
    label: "Billing",
    icon: CreditCard,
    description: "Manage subscriptions"
  },
  {
    path: "/account",
    label: "Account",
    icon: User,
    description: "Profile settings"
  },
];

const bottomNavItems: NavItem[] = [
  {
    path: "/settings",
    label: "Settings",
    icon: Settings,
    description: "App preferences"
  },
  {
    path: "/help",
    label: "Help & Support",
    icon: HelpCircle,
    description: "Get assistance"
  },
];

interface SidebarProps {
  isCollapsed: boolean;
  onToggle: () => void;
}

export function Sidebar({ isCollapsed, onToggle }: SidebarProps) {
  const pathname = usePathname();
  const router = useRouter();
  const [isSigningOut, setIsSigningOut] = useState(false);

  const handleSignOut = async () => {
    setIsSigningOut(true);
    try {
      await authClient.signOut({
        fetchOptions: {
          onSuccess: () => {
            router.push("/");
          },
          onError: (error) => {
            console.error("Sign out error:", error);
            setIsSigningOut(false);
          }
        }
      });
    } catch (error) {
      console.error("Sign out error:", error);
      setIsSigningOut(false);
    }
  };

  return (
    <motion.aside
      className={cn(
        "relative h-full bg-white dark:bg-neutral-900 border-r border-neutral-200 dark:border-neutral-800",
        "flex flex-col shadow-sm z-30",
        "transition-all duration-300 ease-in-out"
      )}
      animate={{
        width: isCollapsed ? 72 : 280,
        minWidth: isCollapsed ? 72 : 280
      }}
      initial={false}
    >
      {/* Header */}
      <div className="flex-shrink-0 border-b border-neutral-200 dark:border-neutral-800">
        {isCollapsed ? (
          /* Collapsed Header */
          <div className="p-4 flex flex-col items-center space-y-3">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.2 }}
              className="w-10 h-10 bg-gradient-to-br from-[#ffbe98] to-[#ff9a56] rounded-xl flex items-center justify-center shadow-lg"
            >
              <Zap className="w-5 h-5 text-white" />
            </motion.div>

            <motion.button
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.1 }}
              onClick={onToggle}
              className="p-2 rounded-xl hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-all duration-200 hover:scale-105 active:scale-95"
              aria-label="Expand sidebar"
            >
              <ChevronRight className="w-4 h-4 text-neutral-600 dark:text-neutral-400" />
            </motion.button>
          </div>
        ) : (
          /* Expanded Header */
          <div className="p-4">
            <div className="flex items-center justify-between">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2, ease: "easeOut" }}
                className="flex items-center gap-3 min-w-0"
              >
                <div className="w-10 h-10 bg-gradient-to-br from-[#ffbe98] to-[#ff9a56] rounded-xl flex items-center justify-center shadow-lg">
                  <Zap className="w-5 h-5 text-white" />
                </div>
                <div className="min-w-0">
                  <h1 className="text-xl font-bold text-neutral-900 dark:text-white tracking-tight">
                    Creem
                  </h1>
                  <p className="text-xs text-neutral-500 dark:text-neutral-400 font-medium">
                    SaaS Platform
                  </p>
                </div>
              </motion.div>

              <button
                onClick={onToggle}
                className="p-2.5 rounded-xl hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-all duration-200 hover:scale-105 active:scale-95"
                aria-label="Collapse sidebar"
              >
                <ChevronLeft className="w-4 h-4 text-neutral-600 dark:text-neutral-400" />
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Navigation */}
      <div className="flex-1 overflow-y-auto py-4">
        <nav className={cn("space-y-1", isCollapsed ? "px-2" : "px-3")}>
          {navItems.map((item, index) => {
            const isActive = pathname === item.path;
            const Icon = item.icon;

            return (
              <motion.div
                key={item.path}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <Link
                  href={item.path}
                  className={cn(
                    "flex items-center rounded-xl transition-all duration-200 group relative",
                    "hover:scale-[1.02] active:scale-[0.98]",
                    isCollapsed ? "justify-center p-3" : "gap-3 px-3 py-3",
                    isActive
                      ? "bg-gradient-to-r from-[#ffbe98]/15 to-[#ff9a56]/10 text-[#ffbe98] shadow-sm border border-[#ffbe98]/20"
                      : "text-neutral-600 dark:text-neutral-400 hover:bg-neutral-100 dark:hover:bg-neutral-800 hover:text-neutral-900 dark:hover:text-neutral-200"
                  )}
                >
                  <Icon className={cn(
                    "w-5 h-5 flex-shrink-0 transition-colors duration-200",
                    isActive ? "text-[#ffbe98]" : "group-hover:text-neutral-700 dark:group-hover:text-neutral-300"
                  )} />

                  {!isCollapsed && (
                    <motion.div
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -10 }}
                      transition={{ duration: 0.2, ease: "easeOut" }}
                      className="flex flex-col flex-1 min-w-0"
                    >
                      <div className="flex items-center justify-between">
                        <span className="font-semibold text-sm truncate">{item.label}</span>
                        {item.badge && (
                          <span className="px-2 py-0.5 text-xs bg-gradient-to-r from-[#ffbe98] to-[#ff9a56] text-white rounded-full font-medium shadow-sm">
                            {item.badge}
                          </span>
                        )}
                      </div>
                      {item.description && (
                        <span className="text-xs text-neutral-500 dark:text-neutral-400 mt-0.5 truncate">
                          {item.description}
                        </span>
                      )}
                    </motion.div>
                  )}

                  {/* Enhanced Tooltip for collapsed state */}
                  {isCollapsed && (
                    <div className="absolute left-full ml-3 px-3 py-2 bg-neutral-900 dark:bg-neutral-700 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-200 pointer-events-none whitespace-nowrap z-50 shadow-lg border border-neutral-700 dark:border-neutral-600">
                      <div className="font-semibold">{item.label}</div>
                      {item.description && (
                        <div className="text-xs text-neutral-300 mt-1">{item.description}</div>
                      )}
                      {item.badge && (
                        <span className="inline-block mt-1 px-1.5 py-0.5 text-xs bg-[#ffbe98] text-white rounded font-medium">
                          {item.badge}
                        </span>
                      )}
                      {/* Tooltip arrow */}
                      <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1 w-2 h-2 bg-neutral-900 dark:bg-neutral-700 rotate-45 border-l border-b border-neutral-700 dark:border-neutral-600"></div>
                    </div>
                  )}
                </Link>
              </motion.div>
            );
          })}
        </nav>

        {/* Divider */}
        <div className={cn("my-4 border-t border-neutral-200 dark:border-neutral-800", isCollapsed ? "mx-2" : "mx-4")} />

        {/* Bottom Navigation */}
        <nav className={cn("space-y-1", isCollapsed ? "px-2" : "px-3")}>
          {bottomNavItems.map((item, index) => {
            const isActive = pathname === item.path;
            const Icon = item.icon;

            return (
              <motion.div
                key={item.path}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: (navItems.length + index) * 0.1 }}
              >
                <Link
                  href={item.path}
                  className={cn(
                    "flex items-center rounded-xl transition-all duration-200 group relative",
                    "hover:scale-[1.02] active:scale-[0.98]",
                    isCollapsed ? "justify-center p-3" : "gap-3 px-3 py-3",
                    isActive
                      ? "bg-gradient-to-r from-[#ffbe98]/15 to-[#ff9a56]/10 text-[#ffbe98] shadow-sm border border-[#ffbe98]/20"
                      : "text-neutral-600 dark:text-neutral-400 hover:bg-neutral-100 dark:hover:bg-neutral-800 hover:text-neutral-900 dark:hover:text-neutral-200"
                  )}
                >
                  <Icon className={cn(
                    "w-5 h-5 flex-shrink-0 transition-colors duration-200",
                    isActive ? "text-[#ffbe98]" : "group-hover:text-neutral-700 dark:group-hover:text-neutral-300"
                  )} />

                  {!isCollapsed && (
                    <motion.div
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -10 }}
                      transition={{ duration: 0.2, ease: "easeOut" }}
                      className="flex flex-col flex-1 min-w-0"
                    >
                      <span className="font-semibold text-sm truncate">{item.label}</span>
                      {item.description && (
                        <span className="text-xs text-neutral-500 dark:text-neutral-400 mt-0.5 truncate">
                          {item.description}
                        </span>
                      )}
                    </motion.div>
                  )}

                  {/* Enhanced Tooltip for collapsed state */}
                  {isCollapsed && (
                    <div className="absolute left-full ml-3 px-3 py-2 bg-neutral-900 dark:bg-neutral-700 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-200 pointer-events-none whitespace-nowrap z-50 shadow-lg border border-neutral-700 dark:border-neutral-600">
                      <div className="font-semibold">{item.label}</div>
                      {item.description && (
                        <div className="text-xs text-neutral-300 mt-1">{item.description}</div>
                      )}
                      {/* Tooltip arrow */}
                      <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1 w-2 h-2 bg-neutral-900 dark:bg-neutral-700 rotate-45 border-l border-b border-neutral-700 dark:border-neutral-600"></div>
                    </div>
                  )}
                </Link>
              </motion.div>
            );
          })}
        </nav>
      </div>

      {/* Sign Out Button */}
      <div className={cn(
        "flex-shrink-0 border-t border-neutral-200 dark:border-neutral-800 bg-neutral-50/50 dark:bg-neutral-800/50",
        isCollapsed ? "p-2" : "p-3"
      )}>
        <motion.button
          onClick={handleSignOut}
          disabled={isSigningOut}
          whileHover={{ scale: isSigningOut ? 1 : 1.02 }}
          whileTap={{ scale: isSigningOut ? 1 : 0.98 }}
          className={cn(
            "flex items-center rounded-xl transition-all duration-200 group relative w-full",
            "text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-950/30",
            "border border-transparent hover:border-red-200 dark:hover:border-red-800/50",
            isCollapsed ? "justify-center p-3" : "gap-3 px-3 py-3",
            isSigningOut && "opacity-50 cursor-not-allowed"
          )}
        >
          <motion.div
            animate={isSigningOut ? { rotate: 360 } : { rotate: 0 }}
            transition={{ duration: 1, repeat: isSigningOut ? Infinity : 0, ease: "linear" }}
          >
            <LogOut className="w-5 h-5 flex-shrink-0" />
          </motion.div>

          {!isCollapsed && (
            <motion.span
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -10 }}
              transition={{ duration: 0.2, ease: "easeOut" }}
              className="font-semibold text-sm"
            >
              {isSigningOut ? "Signing out..." : "Sign out"}
            </motion.span>
          )}

          {/* Enhanced Tooltip for collapsed state */}
          {isCollapsed && (
            <div className="absolute left-full ml-3 px-3 py-2 bg-red-600 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-200 pointer-events-none whitespace-nowrap z-50 shadow-lg">
              <div className="font-semibold">
                {isSigningOut ? "Signing out..." : "Sign out"}
              </div>
              {/* Tooltip arrow */}
              <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1 w-2 h-2 bg-red-600 rotate-45"></div>
            </div>
          )}
        </motion.button>
      </div>
    </motion.aside>
  );
}
