import { ModernDashboardLayout } from "@/components/dashboard/modern-layout";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { BarChart3, TrendingUp, Users, DollarSign } from "lucide-react";

export default function AnalyticsPage() {
  return (
    <ModernDashboardLayout 
      title="Analytics" 
      subtitle="Detailed insights and performance metrics"
    >
      <div className="space-y-6">
        {/* Coming Soon Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-3 text-neutral-900 dark:text-neutral-200">
              <BarChart3 className="w-5 h-5 text-[#ffbe98]" />
              Analytics Dashboard
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12">
              <TrendingUp className="w-16 h-16 text-neutral-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-neutral-900 dark:text-neutral-200 mb-2">
                Analytics Coming Soon
              </h3>
              <p className="text-neutral-600 dark:text-neutral-400 max-w-md mx-auto">
                Advanced analytics and reporting features are currently in development. 
                You&apos;ll be able to track detailed metrics, user behavior, and revenue insights.
              </p>
              <div className="mt-6 flex items-center justify-center gap-4 text-sm text-neutral-500">
                <div className="flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  <span>User Analytics</span>
                </div>
                <div className="flex items-center gap-2">
                  <DollarSign className="w-4 h-4" />
                  <span>Revenue Tracking</span>
                </div>
                <div className="flex items-center gap-2">
                  <BarChart3 className="w-4 h-4" />
                  <span>Performance Metrics</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </ModernDashboardLayout>
  );
}
